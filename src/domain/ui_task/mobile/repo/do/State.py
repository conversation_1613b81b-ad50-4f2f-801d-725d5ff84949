from typing import List, Dict, Optional, Annotated, Callable, Any
from langgraph.graph import add_messages
from pydantic import BaseModel, Field
from typing_extensions import TypedDict


class DeploymentState(TypedDict):
    """
    State machine for task deployment execution
    """

    # Task related
    task: str  # User input task description
    task_id: str  # Task ID for stop control
    task_steps: List[str]  # Parsed individual task steps
    step_wait_times: List[float]  # 等待时间列表
    completed: bool  # Whether the task is completed
    execution_status: str  # Execution status (processing/succeed/failed/terminate)
    retry_count: int  # Current retry count
    max_retries: int  # Maximum retry count
    step_failed: bool  # Whether the current step failed
    error_message: Optional[str]  # Error message if execution failed
    test_case_name: str
    test_case_description: str
    expected_result: str
    app_package: str  # 应用包名
    execution_count: int  # 当前执行次数
    is_restart: bool  # 是否重启app

    # Verification related fields
    verification_mode: str  # "step_by_step" or "aggregation"
    step_expected_results: List[Dict]  # Expected results for each step
    overall_expected_result: Dict  # Overall expected result
    step_verification_results: List[Dict]  # Verification results for each step
    step_retry_counts: List[int]  # Retry count for each step
    max_step_retries: int  # Maximum retries per step (default 3)
    current_step_index: int  # Current step being executed
    agent_config_id: str  # Agent configuration ID

    # New expectation verification fields
    verification_failure_reason: Optional[str]  # Reason for verification failure
    execution_blocked: bool  # Whether execution is blocked due to verification failure
    block_reason: Optional[str]  # Reason for execution blocking

    # Prompt parameterization fields
    app_name: Optional[str]  # Application name for prompt
    app_description: Optional[str]  # Application description for prompt
    ui_component_instructions: Optional[str]  # UI component instructions for prompt
    special_scenarios: Optional[str]  # Special scenarios for prompt

    # Device related
    device: str  # Device ID
    device_type: str  # Device type: android | ios
    device_config: Dict  # Device configuration

    # Supervisor related
    supervisor_state: Optional[Dict[str, Any]]  # 监督状态

    # Page information
    current_page: Dict  # Current page information, including screenshot path and element data

    # Records and messages
    history: List[Dict]  # Execution history records
    messages: Annotated[list, add_messages]  # Message history for React mode
    decision_fields: Optional[Dict[str, Any]]  # Latest decision agent parsed fields

    # Callback
    callback: Optional[Callable[[TypedDict], None]]  # Callback function

    # Reference task related fields (for reference task execution)
    reference_task_id: Optional[str]  # Reference task ID for reference-based execution
    reference_actions: Optional[List[Dict[str, Any]]]  # Reference task action list


class ActionMatch(BaseModel):
    action_id: str = Field(description="High-level action node ID")
    name: str = Field(description="High-level action name")
    match_score: float = Field(description="Match score")
    reason: str = Field(description="Match reason explanation")


class ElementMatch(BaseModel):
    element_id: str = Field(description="Element ID")
    match_score: float = Field(description="Match score")
    screen_element_id: int = Field(description="Screen element ID")
    action_type: str = Field(description="Atomic operation type")
    parameters: Dict[str, Any] = Field(description="Operation parameters")
