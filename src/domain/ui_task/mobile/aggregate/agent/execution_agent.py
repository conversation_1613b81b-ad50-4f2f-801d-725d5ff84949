#!/usr/bin/env python3
"""
执行Agent

负责为需要坐标的动作补充坐标并执行具体操作
"""

import re
import time
from typing import Dict, Any, Tuple, List

from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.execute_prompt import build_execution_prompt
from src.domain.ui_task.mobile.android.action_tool import (
    execute_simple_action,
    parse_action_with_coordinates,
    validate_action
)
from src.domain.ui_task.mobile.android.image_processor import image_annotator
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model


class ExecutionAgent:
    """执行Agent - 负责坐标补充和动作执行"""

    def __init__(self):
        # 使用配置化的坐标模型
        self.ui_tars = get_chat_model(model_name="ui_tars")
        self.vision = get_chat_model(model_name="vision")
        self.model = get_chat_model()
        # 延迟导入避免循环依赖
        self._verification_agent = None

    @staticmethod
    def requires_coordinates(action_str: str) -> bool:
        """
        判断动作是否需要坐标

        Args:
            action_str: 动作字符串

        Returns:
            是否需要坐标
        """
        action_str_lower = action_str.lower()

        # 需要坐标的动作类型
        coordinate_actions = [
            'click', 'long_press', 'drag', 'scroll'
        ]

        # 不需要坐标的动作类型
        non_coordinate_actions = [
            'wait', 'back', 'type', 'delete', 'enter', 'finished'
        ]

        # 检查是否包含需要坐标的动作
        for action in coordinate_actions:
            if action in action_str_lower:
                return True

        # 检查是否包含不需要坐标的动作
        for action in non_coordinate_actions:
            if action in action_str_lower:
                return False

        # 默认情况下，如果无法确定，返回False（不需要坐标）
        return False

    def get_coordinates_from_model(self, decision_fields: Dict[str, Any], action_command: str, image_data_base64: str,
                                   task_id: str, state: DeploymentState, max_retries: int = 3):
        """
        使用坐标模型根据动作决策和命令获取坐标

        Args:
            decision_fields: 决策agent的结构化字段
            action_command: 动作命令
            image_data_base64: 当前截图的base64数据
            task_id: 任务ID
            state: DeploymentState
            max_retries: 最大重试次数

        Returns:
            带坐标的动作字符串
        """
        # 获取设备ID，用于屏幕尺寸检测
        device = state.get("device")

        for attempt in range(max_retries + 1):
            try:
                logger.info(f"[task_id: {task_id}] 🎯 坐标获取尝试 {attempt + 1}/{max_retries + 1}")

                messages = ExecutionAgent._build_coordinate_messages(
                    decision_fields, action_command, image_data_base64
                )

                model_response_obj = self.ui_tars.invoke(messages)
                coordinate_response = model_response_obj.content

                logger.info(
                    f"[task_id: {task_id}] Coordinate model response (attempt {attempt + 1}): \n {coordinate_response}")

                # 解析坐标模型的思考过程和动作
                coordinate_thought, coordinate_action, wrong_reason = ExecutionAgent._parse_coordinate_response(
                    coordinate_response)

                # 如果存在WrongReason且不为空或"None"，跳过动作校验
                if wrong_reason and wrong_reason.strip() and wrong_reason.strip().lower() != "none":
                    logger.info(f"[task_id: {task_id}] ⚠️ 检测到WrongReason，跳过动作校验: {wrong_reason}")
                    # 创建一个表示跳过验证的validation_result
                    validation_result = {
                        "is_valid": True,
                        "errors": [],
                        "action_type_match": True,
                        "coordinates_valid": True,
                        "coordinates_in_bounds": True,
                        "skipped_due_to_wrong_reason": True
                    }
                    result = {
                        "action": coordinate_action,
                        "full_response": coordinate_response,
                        "thought": coordinate_thought,
                        "wrong_reason": wrong_reason,
                        "validation_result": validation_result,
                        "attempt": attempt + 1
                    }
                    logger.info(f"[task_id: {task_id}] ✅ 由于WrongReason跳过验证 (尝试 {attempt + 1}): {wrong_reason}")
                    return result

                # 验证结果
                validation_result = ExecutionAgent._validate_coordinate_result(
                    action_command, coordinate_action, task_id, device
                )

                if validation_result["is_valid"]:
                    # 验证通过，返回结果
                    result = {
                        "action": coordinate_action,
                        "full_response": coordinate_response,
                        "thought": coordinate_thought,
                        "wrong_reason": wrong_reason if wrong_reason and wrong_reason != "None" else "",
                        "validation_result": validation_result,
                        "attempt": attempt + 1
                    }
                    logger.info(f"[task_id: {task_id}] ✅ 坐标获取成功 (尝试 {attempt + 1}): {coordinate_action}")
                    return result
                else:
                    # 验证失败
                    if attempt < max_retries:
                        logger.warning(
                            f"[task_id: {task_id}] ⚠️ 坐标验证失败，准备重试 (尝试 {attempt + 1}): {', '.join(validation_result['errors'])}")
                        # 可以在这里添加延迟
                        continue
                    else:
                        # 达到最大重试次数
                        logger.error(
                            f"[task_id: {task_id}] ❌ 坐标获取失败，已达到最大重试次数: {', '.join(validation_result['errors'])}")
                        return {
                            "action": coordinate_action if coordinate_action else "",
                            "full_response": coordinate_response,
                            "thought": coordinate_thought,
                            "wrong_reason": wrong_reason,
                            "validation_result": validation_result,
                            "attempt": attempt + 1,
                            "error": "验证失败，已达到最大重试次数"
                        }

            except Exception as e:
                logger.error(f"[task_id: {task_id}] ❌ Error getting coordinates (attempt {attempt + 1}): {str(e)}")
                if attempt < max_retries:
                    continue
                else:
                    from src.domain.ui_task.mobile.utils.exception_handler import AgentExceptionHandler
                    return AgentExceptionHandler.handle_execution_agent_exception(
                        task_id, e, max_retries, attempt
                    )

    @staticmethod
    def _build_coordinate_messages(decision_fields: Dict[str, Any], action_command: str,
                                   image_data_base64: str) -> list:
        """
        构建坐标模型的消息列表，包含历史记录和前后图片对比

        Args:
            decision_fields: 决策agent的结构化字段
            action_command: 动作命令
            image_data_base64: 当前截图base64

        Returns:
            消息列表
        """
        messages = []

        # 从结构化字段中提取相关信息
        agent_instruction = decision_fields.get("instruction", "")

        # 根据action_command动态生成动作说明和参数要求
        action_instruction, parameter_instruction = ExecutionAgent._generate_dynamic_action_instruction(action_command)

        # 构建系统指令
        system_instruction = build_execution_prompt(action_instruction, parameter_instruction, agent_instruction)
        print(system_instruction)
        messages.append({
            "role": "user",
            "content": system_instruction
        })
        # 添加当前截图
        messages.append({
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_data_base64}",
                        "detail": "high"
                    },
                    "detail": "high",
                }
            ]
        })
        return messages

    @staticmethod
    def _get_history_parameters(state: DeploymentState) -> Tuple[List[Dict], bool]:
        """
        获取历史参数，仅获取当前步骤的执行历史

        Args:
            state: 当前状态

        Returns:
            Tuple[execution_records, has_execution_history]: 执行记录列表和是否有历史记录
        """
        history = state.get("history", [])
        current_step_index = state.get("current_step_index", 0)

        # 只获取当前步骤的执行记录
        execution_records = [r for r in history if
                             r.get("action") == "step_execution_with_ai" and
                             r.get("step_index") == current_step_index and
                             r.get("ai_response")]

        # 取最近2条记录（如果当前步骤有超过2条记录的话）
        recent_execution_records = execution_records
        has_execution_history = len(recent_execution_records) > 0
        return recent_execution_records, has_execution_history

    @staticmethod
    def _extract_thought_from_coordinate_response(coordinate_response: str) -> str:
        """
        从坐标响应中提取思考内容

        Args:
            coordinate_response: 坐标模型的完整响应

        Returns:
            思考内容
        """
        if not coordinate_response:
            return ""

        lines = coordinate_response.split('\n')
        thought_lines = []

        for i, line in enumerate(lines):
            if line.strip().startswith('Thought:') or line.strip().startswith('思考:'):
                # 提取思考内容（从当前行到Action行之前的所有内容）
                for j in range(i, len(lines)):
                    if lines[j].strip().startswith('Action:'):
                        break
                    thought_lines.append(lines[j])
                break

        thought_content = '\n'.join(thought_lines).strip()
        # 移除"Thought:"前缀
        if thought_content.startswith('Thought:'):
            thought_content = thought_content.replace('Thought:', '').strip()
        elif thought_content.startswith('思考:'):
            thought_content = thought_content.replace('思考:', '').strip()

        return thought_content

    @staticmethod
    def extract_thought_from_ai_response(ai_response: str) -> str:
        """
        从AI响应中提取思考内容，去掉Action等其他部分

        Args:
            ai_response: AI的完整响应内容

        Returns:
            只包含思考内容的字符串
        """
        if not ai_response:
            return ""

        lines = ai_response.split('\n')
        thought_lines = []
        in_thought_section = False

        for line in lines:
            stripped_line = line.strip()

            # 检查是否开始思考部分
            if stripped_line.startswith('Thought:') or stripped_line.startswith('思考:'):
                in_thought_section = True
                # 提取Thought:后面的内容
                if stripped_line.startswith('Thought:'):
                    thought_content = stripped_line.replace('Thought:', '').strip()
                else:
                    thought_content = stripped_line.replace('思考:', '').strip()

                if thought_content:  # 如果同一行有内容
                    thought_lines.append(thought_content)
                continue

            # 如果遇到Action行，停止收集
            if stripped_line.startswith('Action:') or stripped_line.startswith('动作:'):
                break

            # 如果在思考部分，收集内容
            if in_thought_section:
                thought_lines.append(line)

        return '\n'.join(thought_lines).strip()

    @staticmethod
    def _generate_dynamic_action_instruction(action_command: str) -> tuple[str, str]:
        """
        根据action_command动态生成动作说明和参数要求

        Args:
            action_command: 动作命令

        Returns:
            (动作说明, 参数补充要求)
        """
        action_command_lower = action_command.lower()

        # 点击动作
        if 'click' in action_command_lower:
            action_instruction = "click(point='<point>x1 y1</point>')"
            parameter_instruction = "为click动作补充点击位置的坐标"

        # 长按动作
        elif 'long_press' in action_command_lower:
            action_instruction = "long_press(point='<point>x1 y1</point>')"
            parameter_instruction = "为long_press动作补充长按位置的坐标"

        # 拖拽动作
        elif 'drag' in action_command_lower:
            action_instruction = "drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')"
            parameter_instruction = "为drag动作补充起始和结束位置的坐标"

        # 滑动动作
        elif 'scroll' in action_command_lower:
            # 从action_command_lower中提取方向参数
            direction_match = re.search(r"direction='([^']+)'", action_command_lower)
            if direction_match:
                direction = direction_match.group(1)
                action_instruction = f"scroll(point='<point>x1 y1</point>', direction='{direction}')"
            else:
                action_instruction = "scroll(point='<point>x1 y1</point>', direction='down or up or right or left')"
            parameter_instruction = "为scroll动作补充滑动起点坐标，不要改变direction参数"
        # 未知动作类型
        else:
            action_instruction = f"{action_command} - 当前动作"
            parameter_instruction = f"请根据{action_command}的类型补充相应参数，如果不需要坐标则直接输出原始命令"
        return action_instruction, parameter_instruction

    @staticmethod
    def _validate_coordinate_result(action_command: str, coordinate_action: str, task_id: str,
                                    device: str) -> Dict[str, Any]:
        """
        验证坐标模型的输出结果，使用现有的action_tool工具

        Args:
            action_command: 原始动作命令
            coordinate_action: 坐标模型输出的动作
            task_id: 任务ID
            device: 设备ID

        Returns:
            验证结果字典
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "action_type_match": True,
            "coordinates_valid": True,
            "coordinates_in_bounds": True
        }

        if not coordinate_action or coordinate_action.strip() == "":
            validation_result["is_valid"] = False
            validation_result["errors"].append("坐标模型输出为空")
            return validation_result

        # 1. 检查动作类型是否一致
        original_action_type = action_command.lower().split('(')[0].strip()
        output_action_type = coordinate_action.lower().split('(')[0].strip()

        if original_action_type != output_action_type:
            validation_result["is_valid"] = False
            validation_result["action_type_match"] = False
            validation_result["errors"].append(
                f"动作类型不一致: 期望'{original_action_type}', 实际'{output_action_type}'")

        # 2. 使用现有的ActionParser解析动作，传递设备参数进行坐标归一化
        parsed_action = parse_action_with_coordinates(coordinate_action, device)

        if not parsed_action:
            validation_result["is_valid"] = False
            validation_result["coordinates_valid"] = False
            validation_result["errors"].append("无法解析坐标动作")
            return validation_result

        # 3. 使用现有的ActionValidator验证动作
        if not validate_action(parsed_action):
            validation_result["is_valid"] = False
            validation_result["coordinates_valid"] = False
            validation_result["errors"].append("动作验证失败，缺少必要参数")

        if validation_result["is_valid"]:
            logger.info(f"[task_id: {task_id}] ✅ 坐标验证通过: {coordinate_action}")
        else:
            logger.warning(f"[task_id: {task_id}] ❌ 坐标验证失败: {', '.join(validation_result['errors'])}")

        return validation_result

    @staticmethod
    def _parse_coordinate_response(coordinate_response: str) -> Tuple[str, str, str]:
        thought_content = ""
        action_line = ""
        wrong_reason = ""

        lines = coordinate_response.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('Thought:') or line.strip().startswith('思考:'):
                # 提取思考内容（从当前行到Action行之前的所有内容）
                thought_lines = []
                for j in range(i, len(lines)):
                    if lines[j].strip().startswith('Action:'):
                        break
                    thought_lines.append(lines[j])
                thought_content = '\n'.join(thought_lines).strip()
            elif line.strip().startswith('Action:'):
                action_line = line.replace('Action:', '').strip()
            elif line.strip().startswith('WrongReason:'):
                wrong_reason = line.replace('WrongReason:', '').strip()

        return thought_content, action_line, wrong_reason

    def execute_action(self, action_line: str, decision_fields: Dict[str, Any], state: DeploymentState,
                       before_screenshot_path: str) -> \
            Dict[str, Any]:
        """
        执行动作，使用提供的截图数据，不重新截图

        Args:
            action_line: 动作命令
            decision_fields: 决策agent的结构化字段
            state: 当前状态
            before_screenshot_path: 当前截图的base64数据

        Returns:
            执行结果
        """
        task_id = state["task_id"]

        image_data_base64 = convert_screenshot_to_base64(before_screenshot_path, state["task_id"])

        # 开始时间
        start_time = time.time()
        needs_coordinates = ExecutionAgent.requires_coordinates(action_line)

        if needs_coordinates:
            logger.info(f"[task_id: {task_id}] 🎯 Action requires coordinates, using provided screenshot...")

            # 使用提供的截图数据获取坐标，包含验证和重试机制
            coordinate_result = self.get_coordinates_from_model(
                decision_fields, action_line, image_data_base64, task_id, state
            )

            # 处理坐标获取结果
            if isinstance(coordinate_result, dict):
                action_with_coordinates = coordinate_result.get("action", "")
                coordinate_response = coordinate_result.get("full_response", "")
                validation_result = coordinate_result.get("validation_result", {})
                attempt_count = coordinate_result.get("attempt", 1)
                coordinate_error = coordinate_result.get("error", "")
                wrong_reason = coordinate_result.get("wrong_reason", "")

                # 如果解析到了错误原因且不为空，设置到state中
                # 这样 _add_action_history_entry 方法会自动将其添加到执行历史中
                if wrong_reason and wrong_reason.strip():
                    state["verification_failure_reason"] = wrong_reason.strip()
                    logger.info(f"[task_id: {task_id}] 🔍 设置坐标模型错误原因到验证失败原因: {wrong_reason.strip()}")

                # 检查是否获取到有效的坐标
                if action_with_coordinates and validation_result.get("is_valid", False):
                    logger.info(
                        f"[task_id: {task_id}] ✅ 坐标获取成功 (尝试 {attempt_count}次): {action_with_coordinates}")
                    action_result = execute_simple_action(action_with_coordinates, state["device"])
                    action_result["coordinate_response"] = coordinate_response
                    action_result["coordinate_validation"] = validation_result
                    action_result["coordinate_attempts"] = attempt_count
                else:
                    # 坐标获取失败或验证失败
                    error_msg = coordinate_error or "坐标获取失败或验证失败"
                    validation_errors = validation_result.get("errors", [])
                    if validation_errors:
                        error_msg += f": {', '.join(validation_errors)}"

                    logger.exception(f"[task_id: {task_id}] ❌ {error_msg}")
                    action_result = {
                        "status": "error",
                        "message": error_msg,
                        "coordinate_response": coordinate_response,
                        "coordinate_validation": validation_result,
                        "coordinate_attempts": attempt_count,
                        "failed_action": action_with_coordinates
                    }
            else:
                # 兼容旧格式
                action_with_coordinates = coordinate_result if coordinate_result else ""
                if action_with_coordinates:
                    logger.info(f"[task_id: {task_id}] ✓ Got coordinates (legacy): {action_with_coordinates}")
                    action_result = execute_simple_action(action_with_coordinates, state["device"])
                else:
                    action_result = {
                        "status": "error",
                        "message": "Failed to get coordinates (legacy)",
                    }
            # 执行完成后进行截图标注
            self._annotate_screenshot_after_execution(state, action_line, needs_coordinates,
                                                      coordinate_result if needs_coordinates else None,
                                                      before_screenshot_path)
        else:
            logger.info(f"[task_id: {task_id}] ⚡ Executing action without coordinates: {action_line}")
            action_result = execute_simple_action(action_line, state["device"])

        # 结束时间
        end_time = time.time()
        logger.info(f"[task_id: {task_id}] 执行命令耗时: {end_time - start_time:.2f}s")

        # 立即记录执行日志
        self._log_execution_immediately(action_result, needs_coordinates, task_id)

        return action_result

    @staticmethod
    def _annotate_screenshot_after_execution(state: DeploymentState, action_line: str, needs_coordinates: bool,
                                             coordinate_result: Dict[str, Any] = None,
                                             current_screenshot: str = None) -> None:
        """
        在动作执行完成后对截图进行标注

        Args:
            state: 当前状态
            action_line: 原始动作命令
            needs_coordinates: 是否需要坐标
            coordinate_result: 坐标获取结果（如果需要坐标）
        """
        task_id = state["task_id"]

        # 跳过不需要标注的动作
        if not needs_coordinates:
            return

        try:
            # 确定要标注的动作命令
            final_action_command = action_line

            # 如果需要坐标且获取到了坐标，使用带坐标的动作命令
            if needs_coordinates and coordinate_result and isinstance(coordinate_result, dict):
                action_with_coordinates = coordinate_result.get("action", "")
                validation_result = coordinate_result.get("validation_result", {})

                # 只有在坐标验证通过时才使用带坐标的动作
                if action_with_coordinates and validation_result.get("is_valid", False):
                    final_action_command = action_with_coordinates

            # 执行标注（直接在原文件上标注，不返回路径）
            image_annotator.annotate_screenshot_from_action(
                current_screenshot, final_action_command, task_id, device=state["device"]
            )
            logger.info(f"[{task_id}] 🎯 Screenshot annotated after execution: {final_action_command}")

        except Exception as e:
            logger.warning(f"[{task_id}] ⚠️ Failed to annotate screenshot after execution: {str(e)}")

    def _log_execution_immediately(self, action_result: Dict[str, Any], needs_coordinates: bool, task_id: str):
        """立即记录执行日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            # 提取执行思考内容
            thought_content = "执行动作完成"
            if needs_coordinates and action_result:
                coordinate_response = action_result.get("coordinate_response", "")
                if coordinate_response:
                    extracted_thought = self._extract_thought_from_coordinate_response(coordinate_response)
                    if extracted_thought:
                        thought_content = extracted_thought

            execution_log = ExecutionLogService.create_execution_log(thought_content)
            task_persistence_service.append_execution_log_entries(task_id, [execution_log])
            logger.info(f"[{task_id}] 🔧 Execution logged immediately")

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log execution: {str(e)}")