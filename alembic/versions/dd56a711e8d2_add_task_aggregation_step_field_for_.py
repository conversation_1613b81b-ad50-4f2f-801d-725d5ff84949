"""Add task_aggregation_step field for compatibility

Revision ID: dd56a711e8d2
Revises: f5da9c413017
Create Date: 2025-09-04 09:29:52.730667

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dd56a711e8d2'
down_revision: Union[str, Sequence[str], None] = 'f5da9c413017'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add task_aggregation_step column to ui_task table for compatibility
    op.add_column('ui_task', sa.Column('task_aggregation_step', sa.Text(), nullable=True, comment='聚合步骤描述 - 兼容旧版本，新版本统一使用task_step_by_step'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove task_aggregation_step column from ui_task table
    op.drop_column('ui_task', 'task_aggregation_step')
