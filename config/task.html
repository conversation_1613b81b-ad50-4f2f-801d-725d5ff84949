<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrowserMultiply</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            text-align: center;
        }
        .container {
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        #instructions {
            font-size: 18px;
            color: #555;
            margin-bottom: 20px;
        }
        #number-display {
            font-size: 48px;
            font-weight: bold;
            color: #007BFF;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #click-button {
            padding: 15px 30px;
            font-size: 20px;
            cursor: pointer;
            border: none;
            background-color: #28a745;
            color: white;
            border-radius: 5px;
            transition: background-color 0.3s;
            margin-top: 20px;
        }
        #click-button:hover {
            background-color: #218838;
        }
        #form-section {
            margin-top: 30px;
            display: none; /* Initially hidden */
        }
        label {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }
        #result-input {
            padding: 10px;
            font-size: 18px;
            width: 200px;
            margin-top: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #submit-button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            background-color: #007BFF;
            color: white;
            border-radius: 5px;
            transition: background-color 0.3s;
            margin-top: 10px;
        }
        #submit-button:hover {
            background-color: #0056b3;
        }
        #message {
            margin-top: 20px;
            font-size: 18px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>BrowserMultiply</h1>
        <div id="number-display">点击开始</div>
        <button id="click-button">生成数字</button>

        <div id="form-section">
            <label for="result-input">请输入所有数字的乘积:</label>
            <br>
            <input type="number" id="result-input">
            <br>
            <button id="submit-button">提交</button>
        </div>

        <p id="message"></p>
    </div>

    <script>
        let clickCount = 0;
        let numbers = [];
        const maxClicks = 5;
        const numberDisplay = document.getElementById('number-display');
        const clickButton = document.getElementById('click-button');
        const formSection = document.getElementById('form-section');
        const resultInput = document.getElementById('result-input');
        const submitButton = document.getElementById('submit-button');
        const message = document.getElementById('message');

        function generateRandomNumber() {
            return Math.floor(Math.random() * 9) + 2; // Generate numbers from 2 to 10
        }

        clickButton.addEventListener('click', () => {
            if (clickCount < maxClicks) {
                const newNumber = generateRandomNumber();
                numbers.push(newNumber);

                // --- 修复bug的逻辑 ---
                clickCount++; // 先增加计数
                numberDisplay.textContent = newNumber; // 再更新显示

                clickButton.textContent = `生成数字`;

                if (clickCount === maxClicks) {
                    clickButton.disabled = true;
                    // Bug修复：在显示完最后一个数字后，再更新提示信息并显示表单
                    setTimeout(() => {
                        message.textContent = "点击完成，请计算乘积并提交";
                        formSection.style.display = 'block';
                    }, 500); // 增加一个短暂的延迟，让用户有时间看清最后一个数字
                }
            }
        });

        submitButton.addEventListener('click', () => {
            const userProduct = parseInt(resultInput.value);
            const correctProduct = numbers.reduce((a, b) => a * b, 1);

            if (userProduct === correctProduct) {
                message.textContent = `✅ 恭喜，计算正确！乘积是 ${correctProduct}。`;
                message.style.color = 'green';
            } else {
                message.textContent = `❌ 抱歉，计算错误。正确答案是 ${correctProduct}。`;
                message.style.color = 'red';
            }
        });
    </script>

</body>
</html>